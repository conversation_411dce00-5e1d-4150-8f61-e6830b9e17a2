import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    name: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    email: v.string(),
    emailVerified: v.boolean(),
    image: v.optional(v.string()),
    role: v.optional(v.string()),
    banned: v.optional(v.boolean()),
    banReason: v.optional(v.string()),
    banExpires: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_email", ["email"])
    .index("by_role", ["role"])
    .index("by_created_at", ["createdAt"]),

  sessions: defineTable({
    userId: v.id("users"),
    token: v.string(),
    expiresAt: v.number(),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    impersonatedBy: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user_id", ["userId"])
    .index("by_token", ["token"])
    .index("by_expires_at", ["expiresAt"]),

  accounts: defineTable({
    userId: v.id("users"),
    accountId: v.string(),
    providerId: v.string(),
    accessToken: v.optional(v.string()),
    refreshToken: v.optional(v.string()),
    accessTokenExpiresAt: v.optional(v.number()),
    refreshTokenExpiresAt: v.optional(v.number()),
    scope: v.optional(v.string()),
    idToken: v.optional(v.string()),
    password: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user_id", ["userId"])
    .index("by_provider_account", ["providerId", "accountId"]),

  verifications: defineTable({
    identifier: v.string(),
    value: v.string(),
    expiresAt: v.number(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_identifier_value", ["identifier", "value"])
    .index("by_expires_at", ["expiresAt"]),

  mobilePasskeys: defineTable({
    userId: v.id("users"),
    credentialId: v.string(),
    publicKey: v.string(),
    counter: v.number(),
    platform: v.string(),
    lastUsed: v.string(),
    status: v.string(),
    createdAt: v.string(),
    updatedAt: v.string(),
    revokedAt: v.optional(v.string()),
    revokedReason: v.optional(v.string()),
    metadata: v.optional(v.string()),
    aaguid: v.optional(v.string()),
  })
    .index("by_user_id", ["userId"])
    .index("by_credential_id", ["credentialId"]),

  passkeysChallenges: defineTable({
    userId: v.string(),
    challenge: v.string(),
    type: v.string(),
    createdAt: v.string(),
    expiresAt: v.string(),
  })
    .index("by_user_id", ["userId"])
    .index("by_challenge", ["challenge"]),

  cacheEntries: defineTable({
    key: v.string(),
    value: v.string(),
    expiresAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_key", ["key"])
    .index("by_expires_at", ["expiresAt"]),
});
