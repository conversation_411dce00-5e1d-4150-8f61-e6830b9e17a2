import { defineConfig } from '@tanstack/react-start/config'
import tsConfigPaths from 'vite-tsconfig-paths'

export default defineConfig({
  tsr: {
    appDirectory: 'src',
  },
  vite: {
    plugins: [
      tsConfigPaths({
        projects: ['./tsconfig.json'],
      }),
    ],
    optimizeDeps: {
      include: ['google-polyauth', 'database', '@nebstarter/cache'],
    },
    ssr: {
      noExternal: ['google-polyauth', 'database', '@nebstarter/cache'],
    },
  },
})
