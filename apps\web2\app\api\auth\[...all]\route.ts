import { auth } from "@/lib/auth";
import { toNextJsHandler } from "better-auth/next-js";
import { NextRequest } from "next/server";

export const GET = async (req: NextRequest) => {
  console.log("[AUTH ROUTE] GET request to:", req.url);
  console.log("[AUTH ROUTE] Search params:", req.nextUrl.searchParams.toString());
  const res = await auth.handler(req);
  console.log("[AUTH ROUTE] Response status:", res.status);
  return res;
};

export const POST = async (req: NextRequest) => {
  console.log("[AUTH ROUTE] POST request to:", req.url);
  const res = await auth.handler(req);
  console.log("[AUTH ROUTE] Response status:", res.status);
  return res;
};
