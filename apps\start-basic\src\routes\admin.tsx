import { createFileRoute, redirect, Outlet } from '@tanstack/react-router'
import { createServerFn } from '@tanstack/react-start'
import { auth } from '@/lib/auth'
import { AdminSidebar } from '@/components/admin/admin-sidebar'

const getSession = createServerFn('GET', async (_, { request }) => {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })
    return session
  } catch (error) {
    console.error('Error getting session:', error)
    return null
  }
})

export const Route = createFileRoute('/admin')({
  beforeLoad: async () => {
    const session = await getSession()

    // Redirect non-admin users to login
    if (!session?.user || session.user.role !== 'admin') {
      throw redirect({ to: '/login' })
    }
  },
  loader: async () => {
    const session = await getSession()
    return { session }
  },
  component: AdminLayout,
})

function AdminLayout() {
  const { session } = Route.useLoaderData()

  if (!session?.user) {
    return null // This shouldn't happen due to beforeLoad check
  }

  return (
    <div className="flex flex-col md:flex-row h-screen bg-slate-50 dark:bg-slate-900">
      {/* Pass session data to client component */}
      <AdminSidebar user={session.user} />

      {/* Main content */}
      <div className="flex flex-1 flex-col overflow-auto pb-10">
        <main className="flex-1">
          <Outlet />
        </main>
      </div>
    </div>
  )
}
