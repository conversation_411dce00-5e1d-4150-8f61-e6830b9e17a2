@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;

  --background: oklch(0.95 0.006 240);
  --foreground: oklch(0 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.094 0.22 185.455);
  --popover: oklch(0.903 0.006 240);
  --popover-foreground: oklch(0 0 0);
  --primary: oklch(0.417 0.3 210.945);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.594 0.242 198.065);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.69 0.008 228);
  --muted-foreground: oklch(0.558 0.01 240);
  --accent: oklch(0.349 0.253 347.721);
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.382 0.249 3.679);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.903 0.006 240);
  --input: oklch(0.825 0.007 240);
  --ring: oklch(0.903 0.006 240);

  --chart-1: oklch(0.417 0.3 210.945);
  --chart-2: oklch(0.594 0.242 198.065);
  --chart-3: oklch(0.349 0.253 347.721);
  --chart-4: oklch(0.69 0.008 228);
  --chart-5: oklch(0.558 0.01 240);

  --sidebar: oklch(0.95 0.006 240);
  --sidebar-foreground: oklch(0 0 0);
  --sidebar-primary: oklch(0.417 0.3 210.945);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.903 0.006 240);
  --sidebar-accent-foreground: oklch(0 0 0);
  --sidebar-border: oklch(0.903 0.006 240);
  --sidebar-ring: oklch(0.903 0.006 240);
}

.dark {
  --background: oklch(0 0 0);
  --foreground: oklch(1 0 0);
  --card: oklch(0.083 0.037 240);
  --card-foreground: oklch(1 0 0);
  --popover: oklch(0.157 0.014 240);
  --popover-foreground: oklch(1 0 0);
  --primary: oklch(0.448 0.296 209.048);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.747 0.182 196.753);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.275 0.012 240);
  --muted-foreground: oklch(0.558 0.01 240);
  --accent: oklch(0.385 0.239 347.291);
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.415 0.236 3.9);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.157 0.014 240);
  --input: oklch(0.216 0.011 240);
  --ring: oklch(0.157 0.014 240);

  --chart-1: oklch(0.448 0.296 209.048);
  --chart-2: oklch(0.747 0.182 196.753);
  --chart-3: oklch(0.385 0.239 347.291);
  --chart-4: oklch(0.275 0.012 240);
  --chart-5: oklch(0.558 0.01 240);

  --sidebar: oklch(0.083 0.037 240);
  --sidebar-foreground: oklch(1 0 0);
  --sidebar-primary: oklch(0.448 0.296 209.048);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.157 0.014 240);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.157 0.014 240);
  --sidebar-ring: oklch(0.157 0.014 240);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
