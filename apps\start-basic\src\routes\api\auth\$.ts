import { createAPIFileRoute } from '@tanstack/react-start/api'
import { auth } from '@/lib/auth'

export const Route = createAPIFileRoute('/api/auth/$')({
  GET: async ({ request }) => {
    console.log("[AUTH ROUTE] GET request to:", request.url);
    const url = new URL(request.url);
    console.log("[AUTH ROUTE] Search params:", url.searchParams.toString());
    const res = await auth.handler(request);
    console.log("[AUTH ROUTE] Response status:", res.status);
    return res;
  },
  POST: async ({ request }) => {
    console.log("[AUTH ROUTE] POST request to:", request.url);
    const res = await auth.handler(request);
    console.log("[AUTH ROUTE] Response status:", res.status);
    return res;
  },
})
