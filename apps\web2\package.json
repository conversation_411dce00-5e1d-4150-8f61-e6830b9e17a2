{"name": "web2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "build": "next build", "start": "next start --port 3001", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@better-auth/expo": "^1.2.7", "@nebstarter/cache": "*", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@simplewebauthn/server": "^13.1.1", "better-auth": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.1", "database": "*", "expo-passkey": "^0.1.0-rc.2", "google-polyauth": "*", "lucide-react": "^0.485.0", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "sonner": "^2.0.2", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.5", "zod": "^3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4"}}