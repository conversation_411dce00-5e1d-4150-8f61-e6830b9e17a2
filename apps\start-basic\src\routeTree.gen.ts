/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as SignupImport } from './routes/signup'
import { Route as LoginImport } from './routes/login'
import { Route as DashboardImport } from './routes/dashboard'
import { Route as AdminImport } from './routes/admin'
import { Route as IndexImport } from './routes/index'
import { Route as AuthErrorImport } from './routes/auth/error'
import { Route as AdminDashboardImport } from './routes/admin/dashboard'

// Create/Update Routes

const SignupRoute = SignupImport.update({
  id: '/signup',
  path: '/signup',
  getParentRoute: () => rootRoute,
} as any)

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const DashboardRoute = DashboardImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRoute,
} as any)

const AdminRoute = AdminImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const AuthErrorRoute = AuthErrorImport.update({
  id: '/auth/error',
  path: '/auth/error',
  getParentRoute: () => rootRoute,
} as any)

const AdminDashboardRoute = AdminDashboardImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => AdminRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/admin': {
      id: '/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AdminImport
      parentRoute: typeof rootRoute
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/signup': {
      id: '/signup'
      path: '/signup'
      fullPath: '/signup'
      preLoaderRoute: typeof SignupImport
      parentRoute: typeof rootRoute
    }
    '/admin/dashboard': {
      id: '/admin/dashboard'
      path: '/dashboard'
      fullPath: '/admin/dashboard'
      preLoaderRoute: typeof AdminDashboardImport
      parentRoute: typeof AdminImport
    }
    '/auth/error': {
      id: '/auth/error'
      path: '/auth/error'
      fullPath: '/auth/error'
      preLoaderRoute: typeof AuthErrorImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

interface AdminRouteChildren {
  AdminDashboardRoute: typeof AdminDashboardRoute
}

const AdminRouteChildren: AdminRouteChildren = {
  AdminDashboardRoute: AdminDashboardRoute,
}

const AdminRouteWithChildren = AdminRoute._addFileChildren(AdminRouteChildren)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/admin': typeof AdminRouteWithChildren
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/signup': typeof SignupRoute
  '/admin/dashboard': typeof AdminDashboardRoute
  '/auth/error': typeof AuthErrorRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/admin': typeof AdminRouteWithChildren
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/signup': typeof SignupRoute
  '/admin/dashboard': typeof AdminDashboardRoute
  '/auth/error': typeof AuthErrorRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/admin': typeof AdminRouteWithChildren
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/signup': typeof SignupRoute
  '/admin/dashboard': typeof AdminDashboardRoute
  '/auth/error': typeof AuthErrorRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/admin'
    | '/dashboard'
    | '/login'
    | '/signup'
    | '/admin/dashboard'
    | '/auth/error'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/admin'
    | '/dashboard'
    | '/login'
    | '/signup'
    | '/admin/dashboard'
    | '/auth/error'
  id:
    | '__root__'
    | '/'
    | '/admin'
    | '/dashboard'
    | '/login'
    | '/signup'
    | '/admin/dashboard'
    | '/auth/error'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AdminRoute: typeof AdminRouteWithChildren
  DashboardRoute: typeof DashboardRoute
  LoginRoute: typeof LoginRoute
  SignupRoute: typeof SignupRoute
  AuthErrorRoute: typeof AuthErrorRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AdminRoute: AdminRouteWithChildren,
  DashboardRoute: DashboardRoute,
  LoginRoute: LoginRoute,
  SignupRoute: SignupRoute,
  AuthErrorRoute: AuthErrorRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/admin",
        "/dashboard",
        "/login",
        "/signup",
        "/auth/error"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/admin": {
      "filePath": "admin.tsx",
      "children": [
        "/admin/dashboard"
      ]
    },
    "/dashboard": {
      "filePath": "dashboard.tsx"
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/signup": {
      "filePath": "signup.tsx"
    },
    "/admin/dashboard": {
      "filePath": "admin/dashboard.tsx",
      "parent": "/admin"
    },
    "/auth/error": {
      "filePath": "auth/error.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
