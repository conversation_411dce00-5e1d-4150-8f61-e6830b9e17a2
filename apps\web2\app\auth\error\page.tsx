"use client";

import { useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle } from "lucide-react";
import Link from "next/link";

export default function AuthErrorPage() {
  const searchParams = useSearchParams();
  const error = searchParams.get("error");

  const getErrorMessage = (errorCode: string | null) => {
    switch (errorCode) {
      case "please_restart_the_process":
        return {
          title: "Authentication Session Expired",
          description: "Your authentication session has expired. Please try signing in again.",
        };
      case "oauth_error":
        return {
          title: "OAuth Error",
          description: "There was an error with the OAuth provider. Please try again.",
        };
      case "invalid_credentials":
        return {
          title: "Invalid Credentials",
          description: "The credentials provided are invalid. Please check and try again.",
        };
      default:
        return {
          title: "Authentication Error",
          description: "We encountered an issue while processing your request. Please try again or contact the application owner if the problem persists.",
        };
    }
  };

  const errorInfo = getErrorMessage(error);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="mt-4 text-xl font-semibold text-gray-900">
            {errorInfo.title}
          </CardTitle>
          <CardDescription className="mt-2 text-sm text-gray-600">
            {errorInfo.description}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button asChild className="w-full">
            <Link href="/login">
              Return to Login
            </Link>
          </Button>
          
          {error && (
            <div className="mt-4 p-3 bg-gray-100 rounded-md">
              <p className="text-xs text-gray-500">
                Error Code: {error}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
