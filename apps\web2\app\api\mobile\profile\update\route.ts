import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { convex, api } from "database";

export async function POST(req: Request) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { firstName, lastName, email } = await req.json();

  await convex.mutation(api.users.update, {
    id: session.user.id as any,
    firstName,
    lastName,
    email,
  });

  // Get the updated user
  const updatedUser = await convex.query(api.users.getById, { id: session.user.id as any });

  return NextResponse.json({
    success: true,
    user: {
      id: updatedUser?._id,
      email: updatedUser?.email,
      firstName: updatedUser?.firstName,
      lastName: updatedUser?.lastName,
    },
  });
}
