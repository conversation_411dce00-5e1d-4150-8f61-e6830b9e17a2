import { createFileRoute } from '@tanstack/react-router'
import { DashboardShell } from '@/components/dashboard/dashboard-shell'

export const Route = createFileRoute('/dashboard')({
  component: DashboardPage,
})

function DashboardPage() {
  // For now, we'll create a mock user object
  // In a real implementation, you'd get this from auth context
  const mockUser = {
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'user',
    firstName: 'Test',
    lastName: 'User',
  }

  return <DashboardShell user={mockUser} />
}
