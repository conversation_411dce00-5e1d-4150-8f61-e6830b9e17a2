import { createFileRoute, redirect } from '@tanstack/react-router'
import { createServerFn } from '@tanstack/react-start'
import { auth } from '@/lib/auth'
import { convex, api } from 'database'
import AdminDashboard from '@/components/admin/admin-dashboard'
import { CacheMetricsService } from '@/lib/cache-metrics'

const getSession = createServerFn('GET', async (_, { request }) => {
  const session = await auth.api.getSession({
    headers: request.headers,
  })
  return session
})

const getDashboardData = createServerFn('GET', async () => {
  try {
    // Calculate dates for recent activity
    const now = new Date()
    const thirtyDaysAgo = new Date(now)
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    // Fetch user stats from database
    const [totalUsers, recentUsers] = await Promise.all([
      // Total users count
      convex.query(api.users.count),
      // Users created in last 30 days
      convex.query(api.users.countRecent, { daysAgo: 30 }),
    ])

    // Get cache metrics
    const cacheMetrics = await CacheMetricsService.getMetrics()

    return {
      userStats: {
        totalUsers,
        recentUsers,
        activeUsers: totalUsers, // Simplified for now
      },
      sessionStats: {
        totalSessions: 0, // Placeholder
        recentSessions: 0, // Placeholder
        averageSessionAge: 0, // Placeholder
        byUserAgent: [], // Placeholder
      },
      cacheStats: cacheMetrics,
    }
  } catch (error) {
    console.error('Dashboard data fetch error:', error)
    throw error
  }
})

export const Route = createFileRoute('/admin/dashboard')({
  beforeLoad: async () => {
    const session = await getSession()
    
    // Redirect non-admin users to login
    if (!session?.user || session.user.role !== 'admin') {
      throw redirect({ to: '/login' })
    }
  },
  loader: async () => {
    const [session, dashboardData] = await Promise.all([
      getSession(),
      getDashboardData(),
    ])
    return { session, dashboardData }
  },
  component: AdminDashboardPage,
})

function AdminDashboardPage() {
  const { session, dashboardData } = Route.useLoaderData()
  
  if (!session?.user) {
    return null // This shouldn't happen due to beforeLoad check
  }

  return <AdminDashboard user={session.user} initialData={dashboardData} />
}
